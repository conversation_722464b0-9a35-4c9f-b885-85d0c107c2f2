import { Box } from 'theme-ui'
import { Outlet, useLocation } from 'react-router-dom'
import { useEffect } from 'react'
import axios from 'axios'
import { motion } from 'framer-motion'
import useWebSocket from 'react-use-websocket'

import Sidebar from 'components/Sidebar'
import Header from 'components/header'
import { NavBarDisplayContextProvider } from 'context/navBarDisplayContext'
import { SideBarDisplayContextProvider } from 'context/sideBarDisplay'
import { currencyStore } from 'store/currency'
import { fadeAnim } from 'lib/animation'
import { DateTime } from 'luxon'

let AnimatedBox = motion(Box)

const AuthenticatedLayout = () => {
  const { pathname } = useLocation()

  useEffect(() => {
    // fetch ticker once
    axios
      .get(`${process.env.REACT_APP_BASE_URL}/binance-currency-price`)
      .then((res) => {
        let data = res.data.data
        data.forEach((datum: any) => {
          let obj = {
            e: '',
            E: 0,
            s: datum.symbol,
            c: datum.lastPrice,
            p: '',
            P: '',
            v: ''
          }

          if (datum.symbol.toLowerCase() === 'btcusdt') {
            currencyStore.btc = obj
          }
          if (datum.symbol.toLowerCase() === 'ethusdt') {
            currencyStore.eth = obj
          }
          if (datum.symbol.toLowerCase() === 'bnbusdt') {
            currencyStore.bnb = obj
          }
        })
      })
      .catch((err) => {})

    // get vxt initial value first
    // axios
    //   .get('https://api.gateio.ws/api/v4/spot/tickers?currency_pair=VXT_USDT')
    //   .then((res) => {
    //     let data = res.data
    //     data.forEach((datum: any) => {
    //       if (datum.currency_pair.toLowerCase() === 'vxt_usdt') {
    //         currencyStore.vxt = datum.last
    //       }
    //     })
    //   })
    //   .catch((err) => {})
    axios
      .get(`${process.env.REACT_APP_BASE_URL}/currency-price?currency_pair=VXT_USDT`)
      .then((res) => {
        let data = res.data.data
        data.forEach((datum: any) => {
          if (datum.currency_pair.toLowerCase() === 'vxt_usdt') {
            currencyStore.vxt = datum
          }
        })
      })
      .catch((err) => {})
  }, [])

  const { sendMessage } = useWebSocket('wss://api.gateio.ws/ws/v4/', {
    share: true,
    onOpen: () => {
      console.log('Opened connection')
      // Subscribe to the VXT_USDT ticker
      const subscribeMessage = {
        time: DateTime.now().toMillis(),
        channel: 'spot.tickers',
        event: 'subscribe',
        payload: ['VXT_USDT']
      }
      sendMessage(JSON.stringify(subscribeMessage))
    },
    onMessage: (message) => {
      const data = JSON.parse(message.data)
      // Check if message type is ticker update, adjust according to actual API response
      if (data.event === 'update' && data.result) {
        const { currency_pair } = data.result
        if (currency_pair === 'VXT_USDT') {
          currencyStore.vxt = data.result
        }
      }
    }
  })

  return (
    <NavBarDisplayContextProvider>
      <SideBarDisplayContextProvider>
        <>
          <Header />
          <AnimatedBox
            key={pathname}
            variant="layout.mainContainer"
            initial="hide"
            animate="show"
            exit="hide"
            variants={fadeAnim}
          >
            <Outlet />
          </AnimatedBox>
          <Sidebar />
        </>
      </SideBarDisplayContextProvider>
    </NavBarDisplayContextProvider>
  )
}

export default AuthenticatedLayout
