/** @jsxImportSource theme-ui */
import {
  DeepMap,
  FieldError,
  FieldValues,
  get,
  Path,
  PathValue,
  RegisterOptions,
  UnpackNestedValue,
  UseFormRegister,
  UseFormSetValue
} from 'react-hook-form'
import { ErrorMessage } from '@hookform/error-message'
import { Box, Input, InputProps, Label, Text, ThemeUIStyleObject } from 'theme-ui'
import { ReactElement } from 'react'
import PhoneInput from 'react-phone-input-2'
import { useResponsiveValue } from '@theme-ui/match-media'

export type FormInputProps<TFormValues extends FieldValues = FieldValues> = {
  name: Path<TFormValues>
  rules?: RegisterOptions
  register?: UseFormRegister<TFormValues>
  setValue?: UseFormSetValue<TFormValues>
  errors?: Partial<DeepMap<TFormValues, FieldError>>
  label?: string
  heading?: ReactElement
  trailing?: ReactElement
  sx?: ThemeUIStyleObject
} & Omit<InputProps, 'name'>

export const FormPhoneInput = <TFormValues extends FieldValues = FieldValues>({
  name,
  register,
  setValue,
  rules,
  errors,
  className,
  label,
  trailing,
  heading,
  sx,
  ...props
}: FormInputProps<TFormValues>): JSX.Element => {
  const errorMessages = get(errors, name)
  const hasError = !!(errors && errorMessages)
  const phoneInputWidth = useResponsiveValue([250, 350])

  const handleChange = (value: string) => {
    setValue &&
      setValue(name, value as UnpackNestedValue<PathValue<TFormValues, Path<TFormValues>>>)
  }

  return (
    <>
      <Label htmlFor="phone" mb={2}>
        Phone
      </Label>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          bg: 'input',
          borderRadius: 10,
          background: 'input',
          width: '100%',
          height: 60,
          px: 4
        }}
        variant="forms.reactTelInput"
      >
        <Input
          autofillBackgroundColor="input"
          name={name}
          aria-invalid={hasError}
          {...props}
          sx={{
            px: 0,
            width: '100%',
            background: 'none',
            display: 'none'
          }}
          {...(register && register(name, rules))}
        />
        <PhoneInput
          country={'us'}
          enableSearch={true}
          onChange={handleChange}
          dropdownStyle={{
            width: phoneInputWidth
          }}
        />
      </Box>
      {errors != null && (
        <ErrorMessage
          errors={errors}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          name={name as any}
          render={({ message }) => <Text color="red">{message}</Text>}
        />
      )}
    </>
  )
}

export default FormPhoneInput
