{"name": "voxto-pwa", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@fontsource/poppins": "^5.2.6", "@hookform/error-message": "^2.0.1", "@mdx-js/react": "^3.1.0", "@nivo/core": "^0.99.0", "@nivo/line": "^0.99.0", "@nivo/pie": "^0.99.0", "@nivo/tooltip": "^0.99.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@theme-ui/match-media": "^0.17.2", "@theme-ui/tailwind": "^0.17.2", "@types/human-date": "^1.4.5", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.17", "@types/luxon": "^3.6.2", "@types/node": "^22.15.29", "@types/qrcode": "^1.5.5", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-modal": "^3.16.3", "axios": "^1.9.0", "comma-number": "^2.1.0", "country-state-city": "^3.2.1", "framer-motion": "^6.5.1", "human-date": "^1.4.0", "luxon": "^3.6.1", "markdown-to-jsx": "^7.7.6", "nivo": "^0.31.0", "qrcode": "^1.5.4", "qs": "^6.14.0", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.3.1", "react-hook-form": "^7.45.0", "react-icons": "^5.5.0", "react-input-verification-code": "^1.0.2", "react-modal": "^3.16.3", "react-phone-input-2": "^2.15.1", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "react-scroll-percentage": "^4.3.2", "react-stacked-center-carousel": "^1.0.14", "react-use-websocket": "^4.13.0", "styled-components": "^5.3.11", "swr": "^1.3.0", "theme-ui": "^0.17.2", "typescript": "^4.9.5", "usehooks-ts": "^2.16.0", "valtio": "^2.1.5", "web-vitals": "^5.0.2", "workbox-background-sync": "^7.3.0", "workbox-broadcast-update": "^7.3.0", "workbox-cacheable-response": "^7.3.0", "workbox-core": "^7.3.0", "workbox-expiration": "^7.3.0", "workbox-google-analytics": "^7.3.0", "workbox-navigation-preload": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-range-requests": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0", "workbox-streams": "^7.3.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}