{"name": "voxto-pwa", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.9.0", "@fontsource/poppins": "^4.5.5", "@hookform/error-message": "^2.0.0", "@mdx-js/react": "^2.1.1", "@nivo/core": "^0.79.0", "@nivo/line": "^0.79.1", "@nivo/pie": "^0.79.1", "@nivo/tooltip": "^0.79.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "@theme-ui/match-media": "^0.14.5", "@theme-ui/tailwind": "^0.14.2", "@types/human-date": "^1.4.2", "@types/jest": "^26.0.24", "@types/lodash": "^4.14.182", "@types/luxon": "^2.3.2", "@types/node": "^12.20.47", "@types/qrcode": "^1.4.2", "@types/react": "^18.0.12", "@types/react-dom": "^18.0.5", "@types/react-modal": "^3.13.1", "axios": "^0.26.1", "comma-number": "^2.1.0", "country-state-city": "^3.0.1", "framer-motion": "^6.3.3", "human-date": "^1.4.0", "luxon": "^2.4.0", "markdown-to-jsx": "^7.1.7", "nivo": "^0.31.0", "qrcode": "^1.5.0", "qs": "^6.10.3", "react": "^18.1.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.1.0", "react-hook-form": "^7.29.0", "react-icons": "^4.3.1", "react-input-verification-code": "^1.0.1", "react-modal": "^3.15.1", "react-phone-input-2": "^2.15.0", "react-router-dom": "^6.3.0", "react-scripts": "5.0.0", "react-scroll-percentage": "^4.2.0", "react-stacked-center-carousel": "^1.0.7", "react-use-websocket": "^4.2.0", "styled-components": "^5.3.5", "swr": "^1.3.0", "theme-ui": "^0.14.2", "typescript": "^4.6.3", "usehooks-ts": "^2.5.2", "valtio": "^1.5.2", "web-vitals": "^0.2.4", "workbox-background-sync": "^5.1.4", "workbox-broadcast-update": "^5.1.4", "workbox-cacheable-response": "^5.1.4", "workbox-core": "^5.1.4", "workbox-expiration": "^5.1.4", "workbox-google-analytics": "^5.1.4", "workbox-navigation-preload": "^5.1.4", "workbox-precaching": "^5.1.4", "workbox-range-requests": "^5.1.4", "workbox-routing": "^5.1.4", "workbox-strategies": "^5.1.4", "workbox-streams": "^5.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}